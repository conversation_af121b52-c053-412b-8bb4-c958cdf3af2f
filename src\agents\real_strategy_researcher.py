"""
Real Strategy Researcher Agent - Using magistral:24b Model
Actual strategy development and backtesting with real market data.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import logging
import requests
from dataclasses import dataclass
import talib

from src.services.ai_service import ai_service
from src.agents.base_agent import BaseAgent


@dataclass
class TradingStrategy:
    strategy_id: str
    name: str
    description: str
    entry_conditions: List[str]
    exit_conditions: List[str]
    risk_management: Dict[str, float]
    parameters: Dict[str, Any]
    performance_metrics: Dict[str, float]
    created_at: datetime
    last_updated: datetime


@dataclass
class BacktestResult:
    strategy_id: str
    symbol: str
    start_date: datetime
    end_date: datetime
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    avg_trade_return: float
    volatility: float
    calmar_ratio: float


@dataclass
class StrategySignal:
    signal_id: str
    strategy_id: str
    symbol: str
    action: str  # BUY, SELL, HOLD
    confidence: float
    entry_price: float
    stop_loss: float
    take_profit: float
    reasoning: str
    timestamp: datetime


class RealStrategyResearcher(BaseAgent):
    """
    Real Strategy Researcher using magistral:24b for actual strategy development.
    
    Features:
    - Real market data backtesting
    - Technical indicator-based strategies
    - Performance validation with actual data
    - Risk-adjusted strategy scoring
    - Real-time strategy signal generation
    - Strategy optimization and adaptation
    """
    
    def __init__(self):
        super().__init__()
        self.agent_name = "real_strategy_researcher"
        self.model_name = "magistral:24b"
        
        # Strategy storage
        self.active_strategies = {}
        self.backtest_results = {}
        self.strategy_signals = {}
        self.performance_history = {}
        
        # Strategy templates
        self.strategy_templates = {
            "sma_crossover": {
                "name": "SMA Crossover Strategy",
                "description": "Simple Moving Average crossover strategy",
                "parameters": {"fast_period": 10, "slow_period": 30},
                "risk_management": {"stop_loss": 0.05, "take_profit": 0.10}
            },
            "rsi_mean_reversion": {
                "name": "RSI Mean Reversion",
                "description": "RSI-based mean reversion strategy",
                "parameters": {"rsi_period": 14, "oversold": 30, "overbought": 70},
                "risk_management": {"stop_loss": 0.03, "take_profit": 0.06}
            },
            "bollinger_breakout": {
                "name": "Bollinger Band Breakout",
                "description": "Bollinger Band breakout strategy",
                "parameters": {"bb_period": 20, "bb_std": 2, "breakout_threshold": 0.02},
                "risk_management": {"stop_loss": 0.04, "take_profit": 0.08}
            }
        }
        
        # Performance thresholds
        self.performance_thresholds = {
            "min_sharpe_ratio": 1.0,
            "max_drawdown": 0.20,
            "min_win_rate": 0.45,
            "min_total_trades": 20
        }
        
        self.logger = logging.getLogger(self.agent_name)

    async def _initialize_agent(self):
        """Initialize real strategy researcher."""
        self.logger.info(f"🧠 Initializing Real Strategy Researcher with {self.model_name}")
        
        # Initialize strategy templates
        await self._initialize_strategies()
        
        # Load historical data for backtesting
        await self._load_historical_data()
        
        self.logger.info("✅ Real Strategy Researcher initialized")

    async def _initialize_strategies(self):
        """Initialize strategy templates."""
        for template_id, template in self.strategy_templates.items():
            strategy = TradingStrategy(
                strategy_id=f"{template_id}_{datetime.utcnow().strftime('%Y%m%d')}",
                name=template["name"],
                description=template["description"],
                entry_conditions=[],
                exit_conditions=[],
                risk_management=template["risk_management"],
                parameters=template["parameters"],
                performance_metrics={},
                created_at=datetime.utcnow(),
                last_updated=datetime.utcnow()
            )
            
            self.active_strategies[strategy.strategy_id] = strategy
            self.logger.info(f"📋 Initialized strategy: {strategy.name}")

    async def _load_historical_data(self):
        """Load historical market data for backtesting."""
        symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        
        for symbol in symbols:
            try:
                # Get historical klines from Binance (last 1000 1-hour candles)
                url = f"https://api.binance.com/api/v3/klines"
                params = {
                    "symbol": symbol,
                    "interval": "1h",
                    "limit": 1000
                }
                
                response = requests.get(url, params=params, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    
                    # Convert to DataFrame
                    df = pd.DataFrame(data, columns=[
                        'timestamp', 'open', 'high', 'low', 'close', 'volume',
                        'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                        'taker_buy_quote', 'ignore'
                    ])
                    
                    # Convert to proper types
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    for col in ['open', 'high', 'low', 'close', 'volume']:
                        df[col] = df[col].astype(float)
                    
                    # Store for backtesting
                    if not hasattr(self, 'historical_data'):
                        self.historical_data = {}
                    self.historical_data[symbol] = df
                    
                    self.logger.info(f"📊 Loaded {len(df)} historical data points for {symbol}")
                    
            except Exception as e:
                self.logger.error(f"❌ Failed to load historical data for {symbol}: {e}")

    async def _start_agent_tasks(self) -> List[asyncio.Task]:
        """Start real strategy research tasks."""
        return [
            asyncio.create_task(self._strategy_backtesting()),
            asyncio.create_task(self._strategy_optimization()),
            asyncio.create_task(self._real_time_signal_generation()),
            asyncio.create_task(self._performance_monitoring()),
            asyncio.create_task(self._strategy_validation()),
            asyncio.create_task(self._ai_strategy_development())
        ]

    async def _strategy_backtesting(self):
        """Backtest strategies with real historical data."""
        while self.running:
            try:
                for strategy_id, strategy in self.active_strategies.items():
                    if hasattr(self, 'historical_data'):
                        for symbol, data in self.historical_data.items():
                            # Run backtest
                            result = await self._run_backtest(strategy, symbol, data)
                            
                            if result:
                                # Store result
                                result_key = f"{strategy_id}_{symbol}"
                                self.backtest_results[result_key] = result
                                
                                # Update strategy performance
                                strategy.performance_metrics[symbol] = {
                                    "total_return": result.total_return,
                                    "sharpe_ratio": result.sharpe_ratio,
                                    "max_drawdown": result.max_drawdown,
                                    "win_rate": result.win_rate
                                }
                                
                                self.logger.info(f"📈 Backtest {strategy.name} on {symbol}: "
                                               f"Return: {result.total_return:.2%}, "
                                               f"Sharpe: {result.sharpe_ratio:.2f}, "
                                               f"Drawdown: {result.max_drawdown:.2%}")
                
                await asyncio.sleep(3600)  # Backtest every hour
                
            except Exception as e:
                self.logger.error(f"Backtesting error: {e}")
                await asyncio.sleep(600)

    async def _run_backtest(self, strategy: TradingStrategy, symbol: str, data: pd.DataFrame) -> Optional[BacktestResult]:
        """Run actual backtest on historical data."""
        try:
            # Prepare data
            df = data.copy()
            df = df.sort_values('timestamp')
            
            # Calculate technical indicators
            df = await self._calculate_indicators(df)
            
            # Generate signals based on strategy
            signals = await self._generate_backtest_signals(strategy, df)
            
            # Calculate returns
            returns = await self._calculate_backtest_returns(signals, df)
            
            if len(returns) == 0:
                return None
            
            # Calculate performance metrics
            total_return = (returns['portfolio_value'].iloc[-1] / returns['portfolio_value'].iloc[0]) - 1
            daily_returns = returns['portfolio_value'].pct_change().dropna()
            
            if len(daily_returns) == 0:
                return None
            
            sharpe_ratio = (daily_returns.mean() / daily_returns.std()) * np.sqrt(24) if daily_returns.std() > 0 else 0
            
            # Calculate max drawdown
            cumulative = (1 + daily_returns).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            max_drawdown = drawdown.min()
            
            # Calculate win rate
            winning_trades = len([r for r in daily_returns if r > 0])
            total_trades = len(daily_returns)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            result = BacktestResult(
                strategy_id=strategy.strategy_id,
                symbol=symbol,
                start_date=df['timestamp'].iloc[0],
                end_date=df['timestamp'].iloc[-1],
                total_return=total_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=abs(max_drawdown),
                win_rate=win_rate,
                total_trades=total_trades,
                avg_trade_return=daily_returns.mean(),
                volatility=daily_returns.std() * np.sqrt(24),
                calmar_ratio=total_return / abs(max_drawdown) if max_drawdown != 0 else 0
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Backtest calculation error: {e}")
            return None

    async def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for strategy evaluation."""
        try:
            # Moving averages
            df['sma_10'] = talib.SMA(df['close'].values, timeperiod=10)
            df['sma_30'] = talib.SMA(df['close'].values, timeperiod=30)
            df['ema_12'] = talib.EMA(df['close'].values, timeperiod=12)
            df['ema_26'] = talib.EMA(df['close'].values, timeperiod=26)
            
            # RSI
            df['rsi'] = talib.RSI(df['close'].values, timeperiod=14)
            
            # MACD
            macd, macd_signal, macd_hist = talib.MACD(df['close'].values)
            df['macd'] = macd
            df['macd_signal'] = macd_signal
            df['macd_hist'] = macd_hist
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = talib.BBANDS(df['close'].values, timeperiod=20)
            df['bb_upper'] = bb_upper
            df['bb_middle'] = bb_middle
            df['bb_lower'] = bb_lower
            
            # ATR
            df['atr'] = talib.ATR(df['high'].values, df['low'].values, df['close'].values, timeperiod=14)
            
            return df
            
        except Exception as e:
            self.logger.error(f"Indicator calculation error: {e}")
            return df

    async def _generate_backtest_signals(self, strategy: TradingStrategy, df: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals for backtesting."""
        signals = df.copy()
        signals['signal'] = 0  # 0 = hold, 1 = buy, -1 = sell
        
        try:
            if "sma_crossover" in strategy.strategy_id:
                # SMA Crossover strategy
                fast_period = strategy.parameters.get("fast_period", 10)
                slow_period = strategy.parameters.get("slow_period", 30)
                
                signals['signal'] = np.where(
                    signals['sma_10'] > signals['sma_30'], 1,
                    np.where(signals['sma_10'] < signals['sma_30'], -1, 0)
                )
                
            elif "rsi_mean_reversion" in strategy.strategy_id:
                # RSI Mean Reversion strategy
                oversold = strategy.parameters.get("oversold", 30)
                overbought = strategy.parameters.get("overbought", 70)
                
                signals['signal'] = np.where(
                    signals['rsi'] < oversold, 1,
                    np.where(signals['rsi'] > overbought, -1, 0)
                )
                
            elif "bollinger_breakout" in strategy.strategy_id:
                # Bollinger Band Breakout strategy
                signals['signal'] = np.where(
                    signals['close'] > signals['bb_upper'], 1,
                    np.where(signals['close'] < signals['bb_lower'], -1, 0)
                )
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Signal generation error: {e}")
            return signals

    async def _calculate_backtest_returns(self, signals: pd.DataFrame, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate portfolio returns from trading signals."""
        try:
            returns = signals.copy()
            returns['position'] = returns['signal'].shift(1).fillna(0)
            returns['market_return'] = returns['close'].pct_change()
            returns['strategy_return'] = returns['position'] * returns['market_return']
            returns['portfolio_value'] = (1 + returns['strategy_return']).cumprod() * 10000  # Start with $10,000
            
            return returns
            
        except Exception as e:
            self.logger.error(f"Return calculation error: {e}")
            return pd.DataFrame()

    async def _real_time_signal_generation(self):
        """Generate real-time trading signals."""
        while self.running:
            try:
                # Get current market data
                current_data = await self._get_current_market_data()
                
                for strategy_id, strategy in self.active_strategies.items():
                    # Check if strategy meets performance thresholds
                    if await self._strategy_meets_thresholds(strategy):
                        for symbol in ["BTCUSDT", "ETHUSDT", "ADAUSDT"]:
                            signal = await self._generate_real_time_signal(strategy, symbol, current_data)
                            
                            if signal:
                                self.strategy_signals[signal.signal_id] = signal
                                self.logger.info(f"🎯 Signal: {signal.action} {signal.symbol} at ${signal.entry_price:.2f} "
                                               f"(Confidence: {signal.confidence:.2%})")
                
                await asyncio.sleep(300)  # Every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Real-time signal generation error: {e}")
                await asyncio.sleep(120)

    async def _ai_strategy_development(self):
        """Use AI to develop new trading strategies."""
        while self.running:
            try:
                # Analyze current market conditions
                market_analysis = await self._analyze_market_conditions()
                
                # Use AI to suggest new strategies
                ai_strategy_suggestion = await ai_service.generate_response(
                    self.model_name,
                    f"""
                    Based on current market conditions, suggest a new cryptocurrency trading strategy:
                    
                    Market Analysis: {market_analysis}
                    
                    Provide:
                    1. Strategy name and description
                    2. Entry conditions (specific technical indicators)
                    3. Exit conditions (profit taking and stop loss)
                    4. Risk management parameters
                    5. Expected market conditions where this strategy works best
                    6. Specific parameter values for indicators
                    
                    Focus on practical, implementable strategies using common technical indicators.
                    Be specific with numerical thresholds and conditions.
                    """,
                    {"market_conditions": market_analysis}
                )
                
                # Process AI suggestion
                await self._process_ai_strategy_suggestion(ai_strategy_suggestion)
                
                await asyncio.sleep(7200)  # Every 2 hours
                
            except Exception as e:
                self.logger.error(f"AI strategy development error: {e}")
                await asyncio.sleep(1800)

    async def _get_current_market_data(self) -> Dict[str, Any]:
        """Get current market data for signal generation."""
        try:
            symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
            market_data = {}
            
            for symbol in symbols:
                response = requests.get(
                    f"https://api.binance.com/api/v3/ticker/24hr?symbol={symbol}",
                    timeout=5
                )
                
                if response.status_code == 200:
                    data = response.json()
                    market_data[symbol] = {
                        "price": float(data['lastPrice']),
                        "change_24h": float(data['priceChangePercent']),
                        "volume": float(data['volume']),
                        "high": float(data['highPrice']),
                        "low": float(data['lowPrice'])
                    }
            
            return market_data
            
        except Exception as e:
            self.logger.error(f"Error getting current market data: {e}")
            return {}

    async def _strategy_meets_thresholds(self, strategy: TradingStrategy) -> bool:
        """Check if strategy meets performance thresholds."""
        if not strategy.performance_metrics:
            return False
        
        # Check average performance across symbols
        sharpe_ratios = [metrics.get("sharpe_ratio", 0) for metrics in strategy.performance_metrics.values()]
        drawdowns = [metrics.get("max_drawdown", 1) for metrics in strategy.performance_metrics.values()]
        win_rates = [metrics.get("win_rate", 0) for metrics in strategy.performance_metrics.values()]
        
        if not sharpe_ratios:
            return False
        
        avg_sharpe = np.mean(sharpe_ratios)
        max_drawdown = max(drawdowns)
        avg_win_rate = np.mean(win_rates)
        
        return (avg_sharpe >= self.performance_thresholds["min_sharpe_ratio"] and
                max_drawdown <= self.performance_thresholds["max_drawdown"] and
                avg_win_rate >= self.performance_thresholds["min_win_rate"])

    async def get_strategy_performance(self) -> Dict[str, Any]:
        """Get strategy performance summary."""
        performance_summary = {
            "timestamp": datetime.utcnow().isoformat(),
            "total_strategies": len(self.active_strategies),
            "total_backtests": len(self.backtest_results),
            "active_signals": len(self.strategy_signals),
            "strategies": {}
        }
        
        for strategy_id, strategy in self.active_strategies.items():
            strategy_backtests = {k: v for k, v in self.backtest_results.items() if k.startswith(strategy_id)}
            
            if strategy_backtests:
                avg_return = np.mean([result.total_return for result in strategy_backtests.values()])
                avg_sharpe = np.mean([result.sharpe_ratio for result in strategy_backtests.values()])
                max_drawdown = max([result.max_drawdown for result in strategy_backtests.values()])
                
                performance_summary["strategies"][strategy_id] = {
                    "name": strategy.name,
                    "avg_return": avg_return,
                    "avg_sharpe": avg_sharpe,
                    "max_drawdown": max_drawdown,
                    "meets_thresholds": await self._strategy_meets_thresholds(strategy)
                }
        
        return performance_summary

    async def _cleanup_agent(self):
        """Cleanup strategy researcher resources."""
        self.logger.info("🧹 Cleaning up Real Strategy Researcher")
        
        # Clear data structures
        self.active_strategies.clear()
        self.backtest_results.clear()
        self.strategy_signals.clear()
        self.performance_history.clear()

    async def _handle_message(self, message):
        """Handle incoming messages."""
        if message.get("type") == "get_strategies":
            return await self.get_strategy_performance()
        elif message.get("type") == "get_signals":
            return list(self.strategy_signals.values())

    async def _analyze_market_conditions(self) -> Dict[str, Any]:
        """Analyze current market conditions."""
        try:
            market_data = await self._get_current_market_data()
            
            # Calculate market-wide metrics
            if market_data:
                prices = [data["price"] for data in market_data.values()]
                changes = [data["change_24h"] for data in market_data.values()]
                volumes = [data["volume"] for data in market_data.values()]
                
                analysis = {
                    "avg_price_change": np.mean(changes),
                    "market_volatility": np.std(changes),
                    "total_volume": sum(volumes),
                    "bullish_assets": len([c for c in changes if c > 2]),
                    "bearish_assets": len([c for c in changes if c < -2]),
                    "market_sentiment": "bullish" if np.mean(changes) > 1 else "bearish" if np.mean(changes) < -1 else "neutral"
                }
                
                return analysis
            
            return {}
            
        except Exception as e:
            self.logger.error(f"Market analysis error: {e}")
            return {}
