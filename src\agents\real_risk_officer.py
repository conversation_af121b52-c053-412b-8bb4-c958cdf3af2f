"""
Real Risk Officer Agent - Using command-r:35b Model
Actual risk management with real portfolio data and risk calculations.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import logging
import requests
from dataclasses import dataclass
from scipy import stats

from src.services.ai_service import ai_service
from src.agents.base_agent import BaseAgent


@dataclass
class RiskMetrics:
    portfolio_id: str
    total_value: float
    var_95: float
    var_99: float
    expected_shortfall: float
    max_drawdown: float
    volatility: float
    sharpe_ratio: float
    beta: float
    correlation_risk: float
    concentration_risk: float
    liquidity_risk: float
    timestamp: datetime


@dataclass
class RiskAlert:
    alert_id: str
    risk_type: str
    severity: str
    description: str
    current_value: float
    threshold: float
    portfolio_id: str
    affected_positions: List[str]
    recommended_actions: List[str]
    timestamp: datetime


@dataclass
class PortfolioPosition:
    symbol: str
    quantity: float
    market_value: float
    weight: float
    unrealized_pnl: float
    entry_price: float
    current_price: float
    risk_contribution: float


class RealRiskOfficer(BaseAgent):
    """
    Real Risk Officer using command-r:35b for actual risk management.
    
    Features:
    - Real portfolio risk calculations
    - Value at Risk (VaR) calculations
    - Stress testing with actual market data
    - Position sizing recommendations
    - Real-time risk monitoring
    - Correlation analysis
    - Liquidity risk assessment
    """
    
    def __init__(self):
        super().__init__()
        self.agent_name = "real_risk_officer"
        self.model_name = "command-r:35b"
        
        # Risk data storage
        self.portfolio_positions = {}
        self.risk_metrics = {}
        self.risk_alerts = {}
        self.historical_returns = {}
        
        # Risk limits and thresholds
        self.risk_limits = {
            "max_portfolio_var_95": 0.05,      # 5% daily VaR
            "max_portfolio_var_99": 0.08,      # 8% daily VaR
            "max_position_weight": 0.25,       # 25% max single position
            "max_sector_concentration": 0.40,   # 40% max sector exposure
            "max_correlation": 0.80,            # 80% max correlation
            "min_liquidity_ratio": 0.10,        # 10% min liquid assets
            "max_leverage": 3.0,                # 3x max leverage
            "max_drawdown": 0.20                # 20% max drawdown
        }
        
        # Market data for risk calculations
        self.market_data = {}
        self.correlation_matrix = {}
        
        self.logger = logging.getLogger(self.agent_name)

    async def _initialize_agent(self):
        """Initialize real risk officer."""
        self.logger.info(f"🛡️ Initializing Real Risk Officer with {self.model_name}")
        
        # Load market data for risk calculations
        await self._load_market_data()
        
        # Initialize sample portfolio for testing
        await self._initialize_sample_portfolio()
        
        # Calculate initial risk metrics
        await self._calculate_initial_risk_metrics()
        
        self.logger.info("✅ Real Risk Officer initialized")

    async def _load_market_data(self):
        """Load market data for risk calculations."""
        symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT"]
        
        for symbol in symbols:
            try:
                # Get historical data for risk calculations
                url = f"https://api.binance.com/api/v3/klines"
                params = {
                    "symbol": symbol,
                    "interval": "1d",
                    "limit": 100  # Last 100 days
                }
                
                response = requests.get(url, params=params, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    
                    # Convert to DataFrame
                    df = pd.DataFrame(data, columns=[
                        'timestamp', 'open', 'high', 'low', 'close', 'volume',
                        'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                        'taker_buy_quote', 'ignore'
                    ])
                    
                    df['close'] = df['close'].astype(float)
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    
                    # Calculate daily returns
                    df['daily_return'] = df['close'].pct_change()
                    
                    self.historical_returns[symbol] = df['daily_return'].dropna().values
                    self.market_data[symbol] = df
                    
                    self.logger.info(f"📊 Loaded {len(df)} days of data for {symbol}")
                    
            except Exception as e:
                self.logger.error(f"❌ Failed to load data for {symbol}: {e}")

    async def _initialize_sample_portfolio(self):
        """Initialize a sample portfolio for risk testing."""
        # Sample portfolio allocation
        portfolio_allocation = {
            "BTCUSDT": {"weight": 0.40, "quantity": 0.5},
            "ETHUSDT": {"weight": 0.30, "quantity": 2.0},
            "ADAUSDT": {"weight": 0.15, "quantity": 1000.0},
            "BNBUSDT": {"weight": 0.10, "quantity": 5.0},
            "SOLUSDT": {"weight": 0.05, "quantity": 10.0}
        }
        
        # Get current prices and calculate positions
        portfolio_positions = {}
        total_value = 0
        
        for symbol, allocation in portfolio_allocation.items():
            try:
                # Get current price
                response = requests.get(
                    f"https://api.binance.com/api/v3/ticker/price?symbol={symbol}",
                    timeout=5
                )
                
                if response.status_code == 200:
                    price_data = response.json()
                    current_price = float(price_data['price'])
                    
                    quantity = allocation["quantity"]
                    market_value = quantity * current_price
                    total_value += market_value
                    
                    position = PortfolioPosition(
                        symbol=symbol,
                        quantity=quantity,
                        market_value=market_value,
                        weight=0,  # Will calculate after total_value
                        unrealized_pnl=0,  # Simplified for demo
                        entry_price=current_price,  # Assume current price as entry
                        current_price=current_price,
                        risk_contribution=0  # Will calculate later
                    )
                    
                    portfolio_positions[symbol] = position
                    
            except Exception as e:
                self.logger.error(f"Error initializing position for {symbol}: {e}")
        
        # Calculate actual weights
        for symbol, position in portfolio_positions.items():
            position.weight = position.market_value / total_value if total_value > 0 else 0
        
        self.portfolio_positions["main_portfolio"] = portfolio_positions
        self.logger.info(f"💼 Initialized portfolio with ${total_value:,.2f} total value")

    async def _start_agent_tasks(self) -> List[asyncio.Task]:
        """Start real risk monitoring tasks."""
        return [
            asyncio.create_task(self._real_time_risk_monitoring()),
            asyncio.create_task(self._var_calculation()),
            asyncio.create_task(self._correlation_analysis()),
            asyncio.create_task(self._stress_testing()),
            asyncio.create_task(self._position_risk_analysis()),
            asyncio.create_task(self._liquidity_risk_assessment()),
            asyncio.create_task(self._risk_reporting())
        ]

    async def _real_time_risk_monitoring(self):
        """Monitor portfolio risk in real-time."""
        while self.running:
            try:
                for portfolio_id, positions in self.portfolio_positions.items():
                    # Update current prices
                    await self._update_position_prices(portfolio_id)
                    
                    # Calculate current risk metrics
                    risk_metrics = await self._calculate_portfolio_risk(portfolio_id)
                    
                    if risk_metrics:
                        self.risk_metrics[portfolio_id] = risk_metrics
                        
                        # Check risk limits
                        violations = await self._check_risk_limits(portfolio_id, risk_metrics)
                        
                        # Generate alerts for violations
                        for violation in violations:
                            await self._generate_risk_alert(portfolio_id, violation)
                        
                        self.logger.info(f"🛡️ Portfolio {portfolio_id}: VaR95: {risk_metrics.var_95:.2%}, "
                                       f"Vol: {risk_metrics.volatility:.2%}, "
                                       f"Concentration: {risk_metrics.concentration_risk:.2%}")
                
                await asyncio.sleep(60)  # Every minute
                
            except Exception as e:
                self.logger.error(f"Real-time risk monitoring error: {e}")
                await asyncio.sleep(30)

    async def _update_position_prices(self, portfolio_id: str):
        """Update current prices for portfolio positions."""
        if portfolio_id not in self.portfolio_positions:
            return
        
        positions = self.portfolio_positions[portfolio_id]
        
        for symbol, position in positions.items():
            try:
                response = requests.get(
                    f"https://api.binance.com/api/v3/ticker/price?symbol={symbol}",
                    timeout=3
                )
                
                if response.status_code == 200:
                    price_data = response.json()
                    new_price = float(price_data['price'])
                    
                    # Update position
                    old_price = position.current_price
                    position.current_price = new_price
                    position.market_value = position.quantity * new_price
                    position.unrealized_pnl = (new_price - position.entry_price) * position.quantity
                    
                    # Log significant price changes
                    price_change = (new_price - old_price) / old_price if old_price > 0 else 0
                    if abs(price_change) > 0.05:  # 5% change
                        self.logger.info(f"💰 {symbol} price update: ${old_price:.2f} → ${new_price:.2f} ({price_change:+.2%})")
                        
            except Exception as e:
                self.logger.error(f"Error updating price for {symbol}: {e}")

    async def _calculate_portfolio_risk(self, portfolio_id: str) -> Optional[RiskMetrics]:
        """Calculate comprehensive portfolio risk metrics."""
        if portfolio_id not in self.portfolio_positions:
            return None
        
        try:
            positions = self.portfolio_positions[portfolio_id]
            
            # Calculate total portfolio value
            total_value = sum(pos.market_value for pos in positions.values())
            
            # Update position weights
            for position in positions.values():
                position.weight = position.market_value / total_value if total_value > 0 else 0
            
            # Calculate portfolio returns
            portfolio_returns = await self._calculate_portfolio_returns(positions)
            
            if len(portfolio_returns) < 10:  # Need minimum data
                return None
            
            # Calculate VaR
            var_95 = np.percentile(portfolio_returns, 5)  # 5th percentile
            var_99 = np.percentile(portfolio_returns, 1)  # 1st percentile
            
            # Calculate Expected Shortfall (CVaR)
            tail_losses = portfolio_returns[portfolio_returns <= var_95]
            expected_shortfall = np.mean(tail_losses) if len(tail_losses) > 0 else var_95
            
            # Calculate volatility
            volatility = np.std(portfolio_returns) * np.sqrt(252)  # Annualized
            
            # Calculate Sharpe ratio (assuming 2% risk-free rate)
            excess_returns = portfolio_returns - (0.02 / 252)  # Daily risk-free rate
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if np.std(excess_returns) > 0 else 0
            
            # Calculate max drawdown
            cumulative_returns = np.cumprod(1 + portfolio_returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdown)
            
            # Calculate concentration risk
            concentration_risk = max(pos.weight for pos in positions.values())
            
            # Calculate correlation risk
            correlation_risk = await self._calculate_correlation_risk(positions)
            
            # Calculate liquidity risk (simplified)
            liquidity_risk = await self._calculate_liquidity_risk(positions)
            
            risk_metrics = RiskMetrics(
                portfolio_id=portfolio_id,
                total_value=total_value,
                var_95=abs(var_95),
                var_99=abs(var_99),
                expected_shortfall=abs(expected_shortfall),
                max_drawdown=abs(max_drawdown),
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                beta=1.0,  # Simplified - would calculate vs benchmark
                correlation_risk=correlation_risk,
                concentration_risk=concentration_risk,
                liquidity_risk=liquidity_risk,
                timestamp=datetime.utcnow()
            )
            
            return risk_metrics
            
        except Exception as e:
            self.logger.error(f"Portfolio risk calculation error: {e}")
            return None

    async def _calculate_portfolio_returns(self, positions: Dict[str, PortfolioPosition]) -> np.ndarray:
        """Calculate historical portfolio returns."""
        try:
            # Get the minimum length of historical data
            min_length = min(len(self.historical_returns.get(symbol, [])) for symbol in positions.keys())
            
            if min_length < 10:
                return np.array([])
            
            # Calculate weighted portfolio returns
            portfolio_returns = np.zeros(min_length)
            
            for symbol, position in positions.items():
                if symbol in self.historical_returns:
                    asset_returns = self.historical_returns[symbol][-min_length:]
                    portfolio_returns += position.weight * asset_returns
            
            return portfolio_returns
            
        except Exception as e:
            self.logger.error(f"Portfolio returns calculation error: {e}")
            return np.array([])

    async def _calculate_correlation_risk(self, positions: Dict[str, PortfolioPosition]) -> float:
        """Calculate portfolio correlation risk."""
        try:
            symbols = list(positions.keys())
            if len(symbols) < 2:
                return 0.0
            
            # Build correlation matrix
            returns_data = {}
            min_length = float('inf')
            
            for symbol in symbols:
                if symbol in self.historical_returns:
                    returns_data[symbol] = self.historical_returns[symbol]
                    min_length = min(min_length, len(returns_data[symbol]))
            
            if min_length < 10 or len(returns_data) < 2:
                return 0.0
            
            # Truncate to same length
            for symbol in returns_data:
                returns_data[symbol] = returns_data[symbol][-int(min_length):]
            
            # Calculate correlation matrix
            df = pd.DataFrame(returns_data)
            corr_matrix = df.corr()
            
            # Calculate average correlation (excluding diagonal)
            correlations = []
            for i in range(len(corr_matrix)):
                for j in range(i + 1, len(corr_matrix)):
                    correlations.append(abs(corr_matrix.iloc[i, j]))
            
            return np.mean(correlations) if correlations else 0.0
            
        except Exception as e:
            self.logger.error(f"Correlation risk calculation error: {e}")
            return 0.0

    async def _calculate_liquidity_risk(self, positions: Dict[str, PortfolioPosition]) -> float:
        """Calculate portfolio liquidity risk."""
        try:
            # Simplified liquidity risk based on position sizes
            # In reality, this would consider order book depth, trading volumes, etc.
            
            total_value = sum(pos.market_value for pos in positions.values())
            large_positions = sum(pos.market_value for pos in positions.values() if pos.weight > 0.20)
            
            liquidity_risk = large_positions / total_value if total_value > 0 else 0
            
            return liquidity_risk
            
        except Exception as e:
            self.logger.error(f"Liquidity risk calculation error: {e}")
            return 0.0

    async def _check_risk_limits(self, portfolio_id: str, risk_metrics: RiskMetrics) -> List[Dict[str, Any]]:
        """Check portfolio against risk limits."""
        violations = []
        
        # Check VaR limits
        if risk_metrics.var_95 > self.risk_limits["max_portfolio_var_95"]:
            violations.append({
                "type": "var_95_breach",
                "current": risk_metrics.var_95,
                "limit": self.risk_limits["max_portfolio_var_95"],
                "severity": "high"
            })
        
        if risk_metrics.var_99 > self.risk_limits["max_portfolio_var_99"]:
            violations.append({
                "type": "var_99_breach",
                "current": risk_metrics.var_99,
                "limit": self.risk_limits["max_portfolio_var_99"],
                "severity": "critical"
            })
        
        # Check concentration risk
        if risk_metrics.concentration_risk > self.risk_limits["max_position_weight"]:
            violations.append({
                "type": "concentration_breach",
                "current": risk_metrics.concentration_risk,
                "limit": self.risk_limits["max_position_weight"],
                "severity": "medium"
            })
        
        # Check correlation risk
        if risk_metrics.correlation_risk > self.risk_limits["max_correlation"]:
            violations.append({
                "type": "correlation_breach",
                "current": risk_metrics.correlation_risk,
                "limit": self.risk_limits["max_correlation"],
                "severity": "medium"
            })
        
        return violations

    async def _generate_risk_alert(self, portfolio_id: str, violation: Dict[str, Any]):
        """Generate risk alert for limit violations."""
        alert = RiskAlert(
            alert_id=f"risk_{violation['type']}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            risk_type=violation["type"],
            severity=violation["severity"],
            description=f"Risk limit breach: {violation['type']} = {violation['current']:.2%} > {violation['limit']:.2%}",
            current_value=violation["current"],
            threshold=violation["limit"],
            portfolio_id=portfolio_id,
            affected_positions=list(self.portfolio_positions.get(portfolio_id, {}).keys()),
            recommended_actions=await self._get_risk_mitigation_actions(violation),
            timestamp=datetime.utcnow()
        )
        
        self.risk_alerts[alert.alert_id] = alert
        self.logger.warning(f"🚨 RISK ALERT: {alert.description}")

    async def _get_risk_mitigation_actions(self, violation: Dict[str, Any]) -> List[str]:
        """Get recommended actions for risk mitigation."""
        actions = []
        
        if violation["type"] == "var_95_breach":
            actions = [
                "Reduce position sizes to lower portfolio VaR",
                "Increase diversification across uncorrelated assets",
                "Consider hedging with derivatives",
                "Review and tighten stop-loss levels"
            ]
        elif violation["type"] == "concentration_breach":
            actions = [
                "Reduce size of largest position",
                "Diversify into additional assets",
                "Implement position size limits",
                "Rebalance portfolio allocation"
            ]
        elif violation["type"] == "correlation_breach":
            actions = [
                "Add uncorrelated assets to portfolio",
                "Reduce positions in highly correlated assets",
                "Consider alternative asset classes",
                "Monitor correlation changes over time"
            ]
        
        return actions

    async def _stress_testing(self):
        """Perform stress testing on portfolio."""
        while self.running:
            try:
                for portfolio_id in self.portfolio_positions.keys():
                    # Define stress scenarios
                    stress_scenarios = {
                        "market_crash": {"btc_drop": -0.30, "alt_drop": -0.50},
                        "crypto_winter": {"btc_drop": -0.50, "alt_drop": -0.70},
                        "flash_crash": {"btc_drop": -0.20, "alt_drop": -0.30},
                        "correlation_spike": {"correlation_increase": 0.30}
                    }
                    
                    # Run stress tests
                    stress_results = {}
                    for scenario_name, scenario in stress_scenarios.items():
                        result = await self._run_stress_scenario(portfolio_id, scenario)
                        stress_results[scenario_name] = result
                        
                        self.logger.info(f"📉 Stress Test {scenario_name}: Portfolio loss = {result.get('portfolio_loss', 0):.2%}")
                
                await asyncio.sleep(1800)  # Every 30 minutes
                
            except Exception as e:
                self.logger.error(f"Stress testing error: {e}")
                await asyncio.sleep(600)

    async def _run_stress_scenario(self, portfolio_id: str, scenario: Dict[str, float]) -> Dict[str, Any]:
        """Run a specific stress test scenario."""
        try:
            if portfolio_id not in self.portfolio_positions:
                return {}
            
            positions = self.portfolio_positions[portfolio_id]
            total_loss = 0
            
            for symbol, position in positions.items():
                # Apply stress scenario
                if "btc" in symbol.lower() and "btc_drop" in scenario:
                    loss = position.market_value * scenario["btc_drop"]
                    total_loss += loss
                elif "btc" not in symbol.lower() and "alt_drop" in scenario:
                    loss = position.market_value * scenario["alt_drop"]
                    total_loss += loss
            
            total_value = sum(pos.market_value for pos in positions.values())
            portfolio_loss = total_loss / total_value if total_value > 0 else 0
            
            return {
                "portfolio_loss": portfolio_loss,
                "absolute_loss": total_loss,
                "scenario": scenario
            }
            
        except Exception as e:
            self.logger.error(f"Stress scenario calculation error: {e}")
            return {}

    async def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary."""
        summary = {
            "timestamp": datetime.utcnow().isoformat(),
            "portfolios": {},
            "active_alerts": len(self.risk_alerts),
            "risk_limits": self.risk_limits
        }
        
        for portfolio_id, risk_metrics in self.risk_metrics.items():
            summary["portfolios"][portfolio_id] = {
                "total_value": risk_metrics.total_value,
                "var_95": risk_metrics.var_95,
                "var_99": risk_metrics.var_99,
                "volatility": risk_metrics.volatility,
                "sharpe_ratio": risk_metrics.sharpe_ratio,
                "concentration_risk": risk_metrics.concentration_risk,
                "correlation_risk": risk_metrics.correlation_risk,
                "max_drawdown": risk_metrics.max_drawdown
            }
        
        # Add recent alerts
        recent_alerts = sorted(self.risk_alerts.values(), key=lambda x: x.timestamp, reverse=True)[:5]
        summary["recent_alerts"] = [
            {
                "type": alert.risk_type,
                "severity": alert.severity,
                "description": alert.description,
                "timestamp": alert.timestamp.isoformat()
            }
            for alert in recent_alerts
        ]
        
        return summary

    async def _cleanup_agent(self):
        """Cleanup risk officer resources."""
        self.logger.info("🧹 Cleaning up Real Risk Officer")
        
        # Clear data structures
        self.portfolio_positions.clear()
        self.risk_metrics.clear()
        self.risk_alerts.clear()
        self.historical_returns.clear()

    async def _handle_message(self, message):
        """Handle incoming messages."""
        if message.get("type") == "get_risk_summary":
            return await self.get_risk_summary()
        elif message.get("type") == "get_alerts":
            return list(self.risk_alerts.values())

    async def _calculate_initial_risk_metrics(self):
        """Calculate initial risk metrics for all portfolios."""
        for portfolio_id in self.portfolio_positions.keys():
            risk_metrics = await self._calculate_portfolio_risk(portfolio_id)
            if risk_metrics:
                self.risk_metrics[portfolio_id] = risk_metrics
                self.logger.info(f"📊 Initial risk metrics calculated for {portfolio_id}")

    async def _risk_reporting(self):
        """Generate periodic risk reports."""
        while self.running:
            try:
                # Generate hourly risk report
                if datetime.utcnow().minute == 0:
                    risk_summary = await self.get_risk_summary()
                    self.logger.info(f"📊 Hourly Risk Report: {len(risk_summary['portfolios'])} portfolios monitored")
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Risk reporting error: {e}")
                await asyncio.sleep(120)
