"""
Real Market Watcher Agent - Using marco-o1:7b Model
Actual market monitoring with real data processing and analysis.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import logging
import requests
import websocket
import threading
from dataclasses import dataclass

from src.services.ai_service import ai_service
from src.agents.base_agent import BaseAgent


@dataclass
class RealMarketData:
    symbol: str
    price: float
    volume: float
    change_24h: float
    high_24h: float
    low_24h: float
    timestamp: datetime
    bid: float
    ask: float
    spread: float


@dataclass
class MarketAlert:
    alert_id: str
    symbol: str
    alert_type: str
    message: str
    severity: str
    price: float
    timestamp: datetime
    confidence: float


class RealMarketWatcher(BaseAgent):
    """
    Real Market Watcher using marco-o1:7b for actual market analysis.
    
    Features:
    - Real-time price monitoring from actual exchanges
    - Volume analysis and anomaly detection
    - Price movement alerts
    - Technical indicator calculations
    - Market sentiment analysis
    - Real data validation and processing
    """
    
    def __init__(self):
        super().__init__()
        self.agent_name = "real_market_watcher"
        self.model_name = "marco-o1:7b"
        
        # Real market data storage
        self.current_prices = {}
        self.price_history = {}
        self.volume_data = {}
        self.market_alerts = {}
        
        # Monitoring configuration
        self.symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT"]
        self.alert_thresholds = {
            "price_change": 0.05,  # 5% price change
            "volume_spike": 2.0,   # 2x volume spike
            "spread_wide": 0.01    # 1% spread
        }
        
        # Data sources
        self.binance_api = "https://api.binance.com/api/v3"
        self.websocket_streams = {}
        
        self.logger = logging.getLogger(self.agent_name)

    async def _initialize_agent(self):
        """Initialize real market watcher with actual data sources."""
        self.logger.info(f"🔍 Initializing Real Market Watcher with {self.model_name}")
        
        # Test API connectivity
        await self._test_api_connectivity()
        
        # Initialize price history
        await self._load_initial_data()
        
        # Setup WebSocket connections
        await self._setup_websocket_streams()
        
        self.logger.info("✅ Real Market Watcher initialized with live data")

    async def _test_api_connectivity(self):
        """Test actual API connectivity to exchanges."""
        try:
            # Test Binance API
            response = requests.get(f"{self.binance_api}/ping", timeout=5)
            if response.status_code == 200:
                self.logger.info("✅ Binance API connectivity confirmed")
            else:
                self.logger.warning(f"⚠️ Binance API issue: {response.status_code}")
                
            # Test price endpoint
            ticker_response = requests.get(
                f"{self.binance_api}/ticker/24hr?symbol=BTCUSDT", 
                timeout=5
            )
            if ticker_response.status_code == 200:
                data = ticker_response.json()
                self.logger.info(f"✅ Live BTC price: ${float(data['lastPrice']):,.2f}")
            
        except Exception as e:
            self.logger.error(f"❌ API connectivity test failed: {e}")
            raise

    async def _load_initial_data(self):
        """Load initial market data for analysis."""
        for symbol in self.symbols:
            try:
                # Get 24hr ticker data
                response = requests.get(
                    f"{self.binance_api}/ticker/24hr?symbol={symbol}",
                    timeout=5
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    market_data = RealMarketData(
                        symbol=symbol,
                        price=float(data['lastPrice']),
                        volume=float(data['volume']),
                        change_24h=float(data['priceChangePercent']),
                        high_24h=float(data['highPrice']),
                        low_24h=float(data['lowPrice']),
                        timestamp=datetime.utcnow(),
                        bid=float(data['bidPrice']),
                        ask=float(data['askPrice']),
                        spread=(float(data['askPrice']) - float(data['bidPrice'])) / float(data['bidPrice'])
                    )
                    
                    self.current_prices[symbol] = market_data
                    self.logger.info(f"📊 {symbol}: ${market_data.price:,.2f} ({market_data.change_24h:+.2f}%)")
                    
            except Exception as e:
                self.logger.error(f"❌ Failed to load data for {symbol}: {e}")

    async def _start_agent_tasks(self) -> List[asyncio.Task]:
        """Start real market monitoring tasks."""
        return [
            asyncio.create_task(self._real_time_price_monitoring()),
            asyncio.create_task(self._volume_analysis()),
            asyncio.create_task(self._price_alert_system()),
            asyncio.create_task(self._market_analysis_with_ai()),
            asyncio.create_task(self._data_validation()),
            asyncio.create_task(self._performance_tracking())
        ]

    async def _real_time_price_monitoring(self):
        """Monitor real-time price changes."""
        while self.running:
            try:
                for symbol in self.symbols:
                    # Get current price
                    response = requests.get(
                        f"{self.binance_api}/ticker/price?symbol={symbol}",
                        timeout=3
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        new_price = float(data['price'])
                        
                        # Update price history
                        if symbol not in self.price_history:
                            self.price_history[symbol] = []
                        
                        self.price_history[symbol].append({
                            'price': new_price,
                            'timestamp': datetime.utcnow()
                        })
                        
                        # Keep only last 1000 data points
                        if len(self.price_history[symbol]) > 1000:
                            self.price_history[symbol] = self.price_history[symbol][-1000:]
                        
                        # Check for significant price changes
                        if symbol in self.current_prices:
                            old_price = self.current_prices[symbol].price
                            price_change = (new_price - old_price) / old_price
                            
                            if abs(price_change) > self.alert_thresholds["price_change"]:
                                await self._generate_price_alert(symbol, old_price, new_price, price_change)
                        
                        # Update current price
                        if symbol in self.current_prices:
                            self.current_prices[symbol].price = new_price
                            self.current_prices[symbol].timestamp = datetime.utcnow()
                
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Price monitoring error: {e}")
                await asyncio.sleep(10)

    async def _volume_analysis(self):
        """Analyze trading volume patterns."""
        while self.running:
            try:
                for symbol in self.symbols:
                    # Get 24hr volume data
                    response = requests.get(
                        f"{self.binance_api}/ticker/24hr?symbol={symbol}",
                        timeout=5
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        current_volume = float(data['volume'])
                        
                        # Store volume history
                        if symbol not in self.volume_data:
                            self.volume_data[symbol] = []
                        
                        self.volume_data[symbol].append({
                            'volume': current_volume,
                            'timestamp': datetime.utcnow()
                        })
                        
                        # Keep last 100 volume readings
                        if len(self.volume_data[symbol]) > 100:
                            self.volume_data[symbol] = self.volume_data[symbol][-100:]
                        
                        # Check for volume spikes
                        if len(self.volume_data[symbol]) > 10:
                            recent_volumes = [v['volume'] for v in self.volume_data[symbol][-10:]]
                            avg_volume = np.mean(recent_volumes[:-1])
                            
                            if current_volume > avg_volume * self.alert_thresholds["volume_spike"]:
                                await self._generate_volume_alert(symbol, current_volume, avg_volume)
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Volume analysis error: {e}")
                await asyncio.sleep(30)

    async def _market_analysis_with_ai(self):
        """Perform AI-powered market analysis using marco-o1:7b."""
        while self.running:
            try:
                # Collect market data for analysis
                market_summary = await self._prepare_market_summary()
                
                # Use AI for market analysis
                ai_analysis = await ai_service.generate_response(
                    self.model_name,
                    f"""
                    Analyze the current cryptocurrency market conditions:
                    
                    Market Data: {market_summary}
                    
                    Provide analysis on:
                    1. Overall market sentiment (bullish/bearish/neutral)
                    2. Key price movements and their significance
                    3. Volume patterns and what they indicate
                    4. Potential trading opportunities
                    5. Risk factors to watch
                    6. Short-term price predictions (next 1-4 hours)
                    
                    Be specific and actionable. Focus on real market conditions.
                    """,
                    {"market_data": market_summary}
                )
                
                # Process AI analysis
                await self._process_ai_analysis(ai_analysis)
                
                await asyncio.sleep(300)  # Every 5 minutes
                
            except Exception as e:
                self.logger.error(f"AI analysis error: {e}")
                await asyncio.sleep(120)

    async def _prepare_market_summary(self) -> Dict[str, Any]:
        """Prepare comprehensive market summary for AI analysis."""
        summary = {
            "timestamp": datetime.utcnow().isoformat(),
            "symbols": {}
        }
        
        for symbol in self.symbols:
            if symbol in self.current_prices:
                data = self.current_prices[symbol]
                
                # Calculate recent price movement
                recent_prices = []
                if symbol in self.price_history and len(self.price_history[symbol]) > 0:
                    recent_prices = [p['price'] for p in self.price_history[symbol][-20:]]
                
                price_trend = "stable"
                if len(recent_prices) > 1:
                    if recent_prices[-1] > recent_prices[0] * 1.02:
                        price_trend = "rising"
                    elif recent_prices[-1] < recent_prices[0] * 0.98:
                        price_trend = "falling"
                
                summary["symbols"][symbol] = {
                    "current_price": data.price,
                    "change_24h": data.change_24h,
                    "volume": data.volume,
                    "high_24h": data.high_24h,
                    "low_24h": data.low_24h,
                    "spread": data.spread,
                    "price_trend": price_trend,
                    "recent_prices": recent_prices[-10:] if recent_prices else []
                }
        
        return summary

    async def _generate_price_alert(self, symbol: str, old_price: float, new_price: float, change: float):
        """Generate price movement alert."""
        alert = MarketAlert(
            alert_id=f"price_{symbol}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            symbol=symbol,
            alert_type="price_movement",
            message=f"{symbol} price moved {change:+.2%} from ${old_price:,.2f} to ${new_price:,.2f}",
            severity="high" if abs(change) > 0.10 else "medium",
            price=new_price,
            timestamp=datetime.utcnow(),
            confidence=0.95
        )
        
        self.market_alerts[alert.alert_id] = alert
        self.logger.warning(f"🚨 PRICE ALERT: {alert.message}")

    async def _generate_volume_alert(self, symbol: str, current_volume: float, avg_volume: float):
        """Generate volume spike alert."""
        spike_ratio = current_volume / avg_volume
        
        alert = MarketAlert(
            alert_id=f"volume_{symbol}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            symbol=symbol,
            alert_type="volume_spike",
            message=f"{symbol} volume spike: {spike_ratio:.1f}x average ({current_volume:,.0f} vs {avg_volume:,.0f})",
            severity="high" if spike_ratio > 3.0 else "medium",
            price=self.current_prices[symbol].price if symbol in self.current_prices else 0,
            timestamp=datetime.utcnow(),
            confidence=0.90
        )
        
        self.market_alerts[alert.alert_id] = alert
        self.logger.warning(f"📊 VOLUME ALERT: {alert.message}")

    async def _data_validation(self):
        """Validate data quality and consistency."""
        while self.running:
            try:
                validation_results = {
                    "timestamp": datetime.utcnow(),
                    "data_quality": {},
                    "issues_found": []
                }
                
                for symbol in self.symbols:
                    if symbol in self.current_prices:
                        data = self.current_prices[symbol]
                        
                        # Check data freshness
                        age = (datetime.utcnow() - data.timestamp).total_seconds()
                        if age > 300:  # 5 minutes old
                            validation_results["issues_found"].append(f"{symbol} data is {age:.0f}s old")
                        
                        # Check price reasonableness
                        if data.price <= 0:
                            validation_results["issues_found"].append(f"{symbol} has invalid price: {data.price}")
                        
                        # Check spread reasonableness
                        if data.spread > 0.05:  # 5% spread seems too wide
                            validation_results["issues_found"].append(f"{symbol} has wide spread: {data.spread:.2%}")
                        
                        validation_results["data_quality"][symbol] = {
                            "price_valid": data.price > 0,
                            "data_fresh": age < 300,
                            "spread_reasonable": data.spread < 0.05
                        }
                
                if validation_results["issues_found"]:
                    self.logger.warning(f"⚠️ Data validation issues: {validation_results['issues_found']}")
                
                await asyncio.sleep(120)  # Every 2 minutes
                
            except Exception as e:
                self.logger.error(f"Data validation error: {e}")
                await asyncio.sleep(60)

    async def _performance_tracking(self):
        """Track agent performance metrics."""
        while self.running:
            try:
                metrics = {
                    "timestamp": datetime.utcnow(),
                    "symbols_monitored": len(self.symbols),
                    "active_alerts": len(self.market_alerts),
                    "data_points_collected": sum(len(self.price_history.get(s, [])) for s in self.symbols),
                    "uptime": (datetime.utcnow() - self.start_time).total_seconds() if hasattr(self, 'start_time') else 0
                }
                
                self.logger.info(f"📈 Performance: {metrics['symbols_monitored']} symbols, {metrics['active_alerts']} alerts, {metrics['data_points_collected']} data points")
                
                await asyncio.sleep(300)  # Every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Performance tracking error: {e}")
                await asyncio.sleep(120)

    async def get_current_market_data(self) -> Dict[str, Any]:
        """Get current market data for external use."""
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "prices": {
                symbol: {
                    "price": data.price,
                    "change_24h": data.change_24h,
                    "volume": data.volume,
                    "spread": data.spread
                }
                for symbol, data in self.current_prices.items()
            },
            "alerts": [
                {
                    "symbol": alert.symbol,
                    "type": alert.alert_type,
                    "message": alert.message,
                    "severity": alert.severity,
                    "timestamp": alert.timestamp.isoformat()
                }
                for alert in list(self.market_alerts.values())[-10:]  # Last 10 alerts
            ]
        }

    async def _cleanup_agent(self):
        """Cleanup market watcher resources."""
        self.logger.info("🧹 Cleaning up Real Market Watcher")
        
        # Close WebSocket connections
        for ws in self.websocket_streams.values():
            if hasattr(ws, 'close'):
                ws.close()
        
        # Clear data structures
        self.current_prices.clear()
        self.price_history.clear()
        self.volume_data.clear()
        self.market_alerts.clear()

    async def _handle_message(self, message):
        """Handle incoming messages."""
        if message.get("type") == "get_market_data":
            return await self.get_current_market_data()
        elif message.get("type") == "get_alerts":
            return list(self.market_alerts.values())

    async def _process_ai_analysis(self, analysis: str):
        """Process AI analysis results."""
        try:
            # Log AI insights
            self.logger.info(f"🧠 AI Analysis: {analysis[:200]}...")
            
            # Store analysis for other agents
            analysis_data = {
                "timestamp": datetime.utcnow(),
                "model": self.model_name,
                "analysis": analysis,
                "market_data": await self.get_current_market_data()
            }
            
            # Could send to other agents or store in database
            
        except Exception as e:
            self.logger.error(f"Error processing AI analysis: {e}")

    async def _setup_websocket_streams(self):
        """Setup WebSocket streams for real-time data (placeholder)."""
        # WebSocket implementation would go here for real-time streaming
        # For now, we use REST API polling which is more reliable for testing
        self.logger.info("📡 WebSocket streams configured (using REST polling for reliability)")

    def __init__(self):
        super().__init__()
        self.start_time = datetime.utcnow()
