#!/usr/bin/env python3
"""
Advanced AI Orchestration System
Professional-grade AI coordination with multi-agent systems, consensus mechanisms, and intelligent routing
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
import subprocess
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger("AdvancedAIOrchestration")


class AgentRole(Enum):
    """AI Agent roles."""
    MARKET_ANALYST = "market_analyst"
    TECHNICAL_ANALYST = "technical_analyst"
    RISK_MANAGER = "risk_manager"
    PORTFOLIO_MANAGER = "portfolio_manager"
    STRATEGY_RESEARCHER = "strategy_researcher"
    NEWS_ANALYST = "news_analyst"
    EXECUTION_MANAGER = "execution_manager"
    COMPLIANCE_OFFICER = "compliance_officer"
    CHIEF_ANALYST = "chief_analyst"
    DEEPSEEK_RESEARCHER = "deepseek_researcher"  # NEW: deepseek-r1:32b


@dataclass
class AIAgent:
    """AI Agent configuration."""
    name: str
    role: AgentRole
    model: str
    specialization: str
    priority: int
    max_response_time: float
    confidence_threshold: float
    active: bool = True
    performance_score: float = 0.8
    total_calls: int = 0
    successful_calls: int = 0
    avg_response_time: float = 0.0


@dataclass
class AIRequest:
    """AI request data structure."""
    request_id: str
    agent_role: AgentRole
    prompt: str
    context: Dict[str, Any]
    priority: int
    timeout: float
    timestamp: datetime
    metadata: Dict[str, Any]


@dataclass
class AIResponse:
    """AI response data structure."""
    request_id: str
    agent_name: str
    response: str
    confidence: float
    response_time: float
    success: bool
    timestamp: datetime
    metadata: Dict[str, Any]


class ConsensusMethod(Enum):
    """Consensus methods for multi-agent decisions."""
    MAJORITY_VOTE = "majority_vote"
    WEIGHTED_AVERAGE = "weighted_average"
    CONFIDENCE_WEIGHTED = "confidence_weighted"
    EXPERT_OVERRIDE = "expert_override"
    HIERARCHICAL = "hierarchical"


class AdvancedAIOrchestrator:
    """Advanced AI orchestration system."""
    
    def __init__(self):
        self.agents = {}
        self.request_queue = asyncio.Queue()
        self.response_cache = {}
        self.performance_metrics = {}
        self.consensus_history = []
        
        # Orchestration settings
        self.max_concurrent_requests = 5
        self.cache_ttl = 300  # 5 minutes
        self.performance_window = 100  # Last 100 requests
        
        # Initialize semaphore for concurrent requests
        self.request_semaphore = asyncio.Semaphore(self.max_concurrent_requests)
        
        # Initialize agents
        self._initialize_agents()
        
        # Consensus methods
        self.consensus_methods = {
            ConsensusMethod.MAJORITY_VOTE: self._majority_vote_consensus,
            ConsensusMethod.WEIGHTED_AVERAGE: self._weighted_average_consensus,
            ConsensusMethod.CONFIDENCE_WEIGHTED: self._confidence_weighted_consensus,
            ConsensusMethod.EXPERT_OVERRIDE: self._expert_override_consensus,
            ConsensusMethod.HIERARCHICAL: self._hierarchical_consensus
        }
        
    def _initialize_agents(self):
        """Initialize AI agents with their configurations."""
        agent_configs = [
            AIAgent("MarketWatcher", AgentRole.MARKET_ANALYST, "marco-o1:7b", "Market Analysis", 1, 15.0, 0.7),
            AIAgent("TechnicalExpert", AgentRole.TECHNICAL_ANALYST, "cogito:32b", "Technical Analysis", 2, 20.0, 0.75),
            AIAgent("RiskGuardian", AgentRole.RISK_MANAGER, "command-r:35b", "Risk Management", 1, 12.0, 0.8),
            AIAgent("PortfolioMaster", AgentRole.PORTFOLIO_MANAGER, "qwen3:32b", "Portfolio Management", 2, 18.0, 0.75),
            AIAgent("StrategyResearcher", AgentRole.STRATEGY_RESEARCHER, "magistral:24b", "Strategy Research", 3, 25.0, 0.7),
            AIAgent("NewsAnalyzer", AgentRole.NEWS_ANALYST, "gemma3:27b", "News Analysis", 3, 15.0, 0.65),
            AIAgent("ExecutionManager", AgentRole.EXECUTION_MANAGER, "mistral-small:24b", "Trade Execution", 1, 10.0, 0.8),
            AIAgent("ComplianceOfficer", AgentRole.COMPLIANCE_OFFICER, "falcon3:10b", "Compliance", 2, 12.0, 0.85),
            AIAgent("ChiefAnalyst", AgentRole.CHIEF_ANALYST, "granite3.3:8b", "Strategic Oversight", 1, 20.0, 0.9),
            AIAgent("DeepSeekResearcher", AgentRole.DEEPSEEK_RESEARCHER, "deepseek-r1:32b", "Advanced Research & Reasoning", 1, 30.0, 0.95)
        ]
        
        for agent in agent_configs:
            self.agents[agent.name] = agent
            
        logger.info(f"✅ Initialized {len(self.agents)} AI agents")
    
    async def process_request(self, request: AIRequest) -> AIResponse:
        """Process a single AI request."""
        async with self.request_semaphore:
            # Check cache first
            cache_key = self._generate_cache_key(request)
            if cache_key in self.response_cache:
                cached_response = self.response_cache[cache_key]
                if (datetime.now(timezone.utc) - cached_response.timestamp).total_seconds() < self.cache_ttl:
                    logger.info(f"🔄 Cache hit for request {request.request_id}")
                    return cached_response
            
            # Find appropriate agent
            agent = self._select_agent(request.agent_role)
            if not agent:
                return AIResponse(
                    request_id=request.request_id,
                    agent_name="SYSTEM",
                    response="No suitable agent available",
                    confidence=0.0,
                    response_time=0.0,
                    success=False,
                    timestamp=datetime.now(timezone.utc),
                    metadata={"error": "No agent available"}
                )
            
            # Execute request
            start_time = time.time()
            
            try:
                # Prepare enhanced prompt
                enhanced_prompt = self._enhance_prompt(request, agent)
                
                # Call AI model
                response_text = await self._call_ai_model(agent, enhanced_prompt, request.timeout)
                
                response_time = time.time() - start_time
                
                # Calculate confidence based on response quality
                confidence = self._calculate_response_confidence(response_text, agent)
                
                # Create response
                ai_response = AIResponse(
                    request_id=request.request_id,
                    agent_name=agent.name,
                    response=response_text,
                    confidence=confidence,
                    response_time=response_time,
                    success=True,
                    timestamp=datetime.now(timezone.utc),
                    metadata={"agent_role": agent.role.value, "model": agent.model}
                )
                
                # Update agent performance
                self._update_agent_performance(agent, ai_response)
                
                # Cache response
                self.response_cache[cache_key] = ai_response
                
                logger.info(f"✅ AI Request {request.request_id} completed by {agent.name} in {response_time:.2f}s")
                
                return ai_response
                
            except Exception as e:
                response_time = time.time() - start_time
                logger.error(f"❌ AI Request {request.request_id} failed: {e}")
                
                # Update agent performance for failure
                agent.total_calls += 1
                
                return AIResponse(
                    request_id=request.request_id,
                    agent_name=agent.name,
                    response=f"AI processing error: {str(e)}",
                    confidence=0.0,
                    response_time=response_time,
                    success=False,
                    timestamp=datetime.now(timezone.utc),
                    metadata={"error": str(e), "agent_role": agent.role.value}
                )
    
    async def multi_agent_consensus(self, request: AIRequest, 
                                  agent_roles: List[AgentRole],
                                  consensus_method: ConsensusMethod = ConsensusMethod.CONFIDENCE_WEIGHTED) -> Dict[str, Any]:
        """Get consensus from multiple agents."""
        # Create requests for each agent
        requests = []
        for i, role in enumerate(agent_roles):
            agent_request = AIRequest(
                request_id=f"{request.request_id}_agent_{i}",
                agent_role=role,
                prompt=request.prompt,
                context=request.context,
                priority=request.priority,
                timeout=request.timeout,
                timestamp=datetime.now(timezone.utc),
                metadata=request.metadata
            )
            requests.append(agent_request)
        
        # Process requests concurrently
        tasks = [self.process_request(req) for req in requests]
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful responses
        successful_responses = [r for r in responses if isinstance(r, AIResponse) and r.success]
        
        if not successful_responses:
            return {
                "consensus": "No successful responses",
                "confidence": 0.0,
                "participating_agents": 0,
                "method": consensus_method.value,
                "error": "All agents failed to respond"
            }
        
        # Apply consensus method
        consensus_func = self.consensus_methods.get(consensus_method)
        if not consensus_func:
            consensus_func = self._confidence_weighted_consensus
        
        consensus_result = consensus_func(successful_responses)
        
        # Store consensus history
        consensus_record = {
            "request_id": request.request_id,
            "method": consensus_method.value,
            "participating_agents": len(successful_responses),
            "consensus": consensus_result,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "individual_responses": [asdict(r) for r in successful_responses]
        }
        
        self.consensus_history.append(consensus_record)
        if len(self.consensus_history) > 1000:  # Keep last 1000 records
            self.consensus_history = self.consensus_history[-1000:]
        
        logger.info(f"🤝 Multi-agent consensus completed: {len(successful_responses)} agents, method: {consensus_method.value}")
        
        return consensus_result
    
    async def intelligent_routing(self, request: AIRequest) -> AIResponse:
        """Intelligently route request to best available agent."""
        # Get agents for the requested role
        suitable_agents = [agent for agent in self.agents.values() 
                          if agent.role == request.agent_role and agent.active]
        
        if not suitable_agents:
            # Fallback to any available agent
            suitable_agents = [agent for agent in self.agents.values() if agent.active]
        
        if not suitable_agents:
            return AIResponse(
                request_id=request.request_id,
                agent_name="SYSTEM",
                response="No agents available",
                confidence=0.0,
                response_time=0.0,
                success=False,
                timestamp=datetime.now(timezone.utc),
                metadata={"error": "No agents available"}
            )
        
        # Score agents based on performance, availability, and specialization
        agent_scores = []
        for agent in suitable_agents:
            score = self._calculate_agent_score(agent, request)
            agent_scores.append((agent, score))
        
        # Sort by score (highest first)
        agent_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Try agents in order of score
        for agent, score in agent_scores:
            try:
                # Create a copy of the request for this agent
                routed_request = AIRequest(
                    request_id=request.request_id,
                    agent_role=agent.role,
                    prompt=request.prompt,
                    context=request.context,
                    priority=request.priority,
                    timeout=request.timeout,
                    timestamp=request.timestamp,
                    metadata={**request.metadata, "routing_score": score}
                )
                
                response = await self.process_request(routed_request)
                
                if response.success and response.confidence >= agent.confidence_threshold:
                    logger.info(f"🎯 Intelligent routing successful: {agent.name} (score: {score:.2f})")
                    return response
                
            except Exception as e:
                logger.warning(f"⚠️ Agent {agent.name} failed during intelligent routing: {e}")
                continue
        
        # If all agents failed, return the last response
        return response if 'response' in locals() else AIResponse(
            request_id=request.request_id,
            agent_name="SYSTEM",
            response="All agents failed",
            confidence=0.0,
            response_time=0.0,
            success=False,
            timestamp=datetime.now(timezone.utc),
            metadata={"error": "All agents failed"}
        )
    
    def _select_agent(self, role: AgentRole) -> Optional[AIAgent]:
        """Select best agent for the given role."""
        suitable_agents = [agent for agent in self.agents.values() 
                          if agent.role == role and agent.active]
        
        if not suitable_agents:
            return None
        
        # Return agent with highest performance score
        return max(suitable_agents, key=lambda a: a.performance_score)
    
    def _enhance_prompt(self, request: AIRequest, agent: AIAgent) -> str:
        """Enhance prompt with context and agent-specific instructions."""
        base_prompt = request.prompt
        
        # Add context if available
        if request.context:
            context_str = json.dumps(request.context, indent=2, default=str)
            base_prompt = f"Context:\n{context_str}\n\nTask: {base_prompt}"
        
        # Add agent-specific instructions
        role_instructions = {
            AgentRole.MARKET_ANALYST: "Provide comprehensive market analysis with data-driven insights.",
            AgentRole.TECHNICAL_ANALYST: "Focus on technical indicators, chart patterns, and price action analysis.",
            AgentRole.RISK_MANAGER: "Emphasize risk assessment, potential downsides, and risk mitigation strategies.",
            AgentRole.PORTFOLIO_MANAGER: "Consider portfolio optimization, asset allocation, and diversification.",
            AgentRole.STRATEGY_RESEARCHER: "Analyze trading strategies, backtesting results, and optimization opportunities.",
            AgentRole.NEWS_ANALYST: "Interpret news impact on markets and sentiment analysis.",
            AgentRole.EXECUTION_MANAGER: "Focus on trade execution, timing, and market microstructure.",
            AgentRole.COMPLIANCE_OFFICER: "Ensure regulatory compliance and identify potential violations.",
            AgentRole.CHIEF_ANALYST: "Provide strategic oversight and synthesize multiple perspectives."
        }
        
        instruction = role_instructions.get(agent.role, "Provide professional analysis.")
        enhanced_prompt = f"{instruction}\n\n{base_prompt}\n\nProvide concise, actionable insights."
        
        return enhanced_prompt
    
    async def _call_ai_model(self, agent: AIAgent, prompt: str, timeout: float) -> str:
        """Call AI model with timeout handling."""
        try:
            result = await asyncio.wait_for(
                self._execute_ollama_call(agent.model, prompt),
                timeout=timeout
            )
            return result
        except asyncio.TimeoutError:
            raise Exception(f"AI model {agent.model} timed out after {timeout}s")
    
    async def _execute_ollama_call(self, model: str, prompt: str) -> str:
        """Execute Ollama call asynchronously."""
        loop = asyncio.get_event_loop()
        
        def run_ollama():
            result = subprocess.run(
                ["ollama", "run", model, prompt],
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8',
                errors='replace'
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                raise Exception(f"Ollama error: {result.stderr}")
        
        return await loop.run_in_executor(None, run_ollama)
    
    def _calculate_response_confidence(self, response: str, agent: AIAgent) -> float:
        """Calculate confidence score for response."""
        base_confidence = agent.confidence_threshold
        
        # Adjust based on response length (longer responses might be more detailed)
        length_factor = min(len(response) / 1000, 1.0)  # Cap at 1000 chars
        
        # Adjust based on agent performance
        performance_factor = agent.performance_score
        
        # Simple confidence calculation
        confidence = base_confidence * (0.7 + 0.2 * length_factor + 0.1 * performance_factor)
        
        return min(confidence, 1.0)
    
    def _update_agent_performance(self, agent: AIAgent, response: AIResponse):
        """Update agent performance metrics."""
        agent.total_calls += 1
        
        if response.success:
            agent.successful_calls += 1
        
        # Update average response time
        if agent.avg_response_time == 0:
            agent.avg_response_time = response.response_time
        else:
            # Exponential moving average
            alpha = 0.1
            agent.avg_response_time = alpha * response.response_time + (1 - alpha) * agent.avg_response_time
        
        # Update performance score
        success_rate = agent.successful_calls / agent.total_calls
        time_performance = max(0, 1 - (agent.avg_response_time / agent.max_response_time))
        
        agent.performance_score = 0.7 * success_rate + 0.3 * time_performance
    
    def _calculate_agent_score(self, agent: AIAgent, request: AIRequest) -> float:
        """Calculate agent suitability score for request."""
        # Base score from performance
        base_score = agent.performance_score
        
        # Priority bonus (higher priority agents get bonus for high priority requests)
        priority_bonus = 0.1 if agent.priority <= request.priority else 0.0
        
        # Availability bonus (faster agents get bonus)
        availability_bonus = max(0, 0.2 * (1 - agent.avg_response_time / agent.max_response_time))
        
        # Specialization bonus (exact role match)
        specialization_bonus = 0.2 if agent.role == request.agent_role else 0.0
        
        total_score = base_score + priority_bonus + availability_bonus + specialization_bonus
        
        return min(total_score, 1.0)
    
    def _generate_cache_key(self, request: AIRequest) -> str:
        """Generate cache key for request."""
        # Simple hash of prompt and context
        content = f"{request.prompt}_{json.dumps(request.context, sort_keys=True, default=str)}"
        return f"{request.agent_role.value}_{hash(content)}"

    # Consensus Methods
    def _majority_vote_consensus(self, responses: List[AIResponse]) -> Dict[str, Any]:
        """Majority vote consensus method."""
        # Extract key decisions/recommendations from responses
        decisions = []
        for response in responses:
            # Simple keyword extraction for decisions
            text = response.response.lower()
            if "buy" in text or "bullish" in text or "positive" in text:
                decisions.append("BUY")
            elif "sell" in text or "bearish" in text or "negative" in text:
                decisions.append("SELL")
            else:
                decisions.append("HOLD")

        # Count votes
        vote_counts = {}
        for decision in decisions:
            vote_counts[decision] = vote_counts.get(decision, 0) + 1

        # Find majority
        majority_decision = max(vote_counts, key=vote_counts.get)
        majority_count = vote_counts[majority_decision]

        confidence = majority_count / len(decisions)

        return {
            "consensus": majority_decision,
            "confidence": round(confidence, 4),
            "vote_distribution": vote_counts,
            "method": "majority_vote",
            "participating_agents": len(responses)
        }

    def _weighted_average_consensus(self, responses: List[AIResponse]) -> Dict[str, Any]:
        """Weighted average consensus based on agent performance."""
        if not responses:
            return {"consensus": "No responses", "confidence": 0.0}

        # Get agent performance weights
        weights = []
        values = []

        for response in responses:
            agent = self.agents.get(response.agent_name)
            weight = agent.performance_score if agent else 0.5
            weights.append(weight)

            # Extract numerical value from response (simplified)
            value = self._extract_numerical_sentiment(response.response)
            values.append(value)

        # Calculate weighted average
        total_weight = sum(weights)
        if total_weight == 0:
            weighted_avg = np.mean(values)
            confidence = 0.5
        else:
            weighted_avg = sum(w * v for w, v in zip(weights, values)) / total_weight
            confidence = min(total_weight / len(weights), 1.0)

        # Convert back to decision
        if weighted_avg > 0.6:
            consensus = "BUY"
        elif weighted_avg < 0.4:
            consensus = "SELL"
        else:
            consensus = "HOLD"

        return {
            "consensus": consensus,
            "confidence": round(confidence, 4),
            "weighted_score": round(weighted_avg, 4),
            "method": "weighted_average",
            "participating_agents": len(responses)
        }

    def _confidence_weighted_consensus(self, responses: List[AIResponse]) -> Dict[str, Any]:
        """Confidence-weighted consensus method."""
        if not responses:
            return {"consensus": "No responses", "confidence": 0.0}

        # Weight by response confidence
        weighted_decisions = []
        total_confidence = 0

        for response in responses:
            decision_value = self._extract_numerical_sentiment(response.response)
            weight = response.confidence

            weighted_decisions.append(decision_value * weight)
            total_confidence += weight

        if total_confidence == 0:
            consensus_value = np.mean([self._extract_numerical_sentiment(r.response) for r in responses])
            confidence = 0.5
        else:
            consensus_value = sum(weighted_decisions) / total_confidence
            confidence = total_confidence / len(responses)

        # Convert to decision
        if consensus_value > 0.6:
            consensus = "BUY"
        elif consensus_value < 0.4:
            consensus = "SELL"
        else:
            consensus = "HOLD"

        return {
            "consensus": consensus,
            "confidence": round(confidence, 4),
            "consensus_score": round(consensus_value, 4),
            "method": "confidence_weighted",
            "participating_agents": len(responses),
            "individual_confidences": [r.confidence for r in responses]
        }

    def _expert_override_consensus(self, responses: List[AIResponse]) -> Dict[str, Any]:
        """Expert override consensus - highest priority agent wins."""
        if not responses:
            return {"consensus": "No responses", "confidence": 0.0}

        # Find highest priority agent
        expert_response = None
        highest_priority = float('inf')

        for response in responses:
            agent = self.agents.get(response.agent_name)
            if agent and agent.priority < highest_priority:  # Lower number = higher priority
                highest_priority = agent.priority
                expert_response = response

        if not expert_response:
            expert_response = responses[0]  # Fallback

        decision_value = self._extract_numerical_sentiment(expert_response.response)

        if decision_value > 0.6:
            consensus = "BUY"
        elif decision_value < 0.4:
            consensus = "SELL"
        else:
            consensus = "HOLD"

        return {
            "consensus": consensus,
            "confidence": expert_response.confidence,
            "expert_agent": expert_response.agent_name,
            "expert_priority": highest_priority,
            "method": "expert_override",
            "participating_agents": len(responses)
        }

    def _hierarchical_consensus(self, responses: List[AIResponse]) -> Dict[str, Any]:
        """Hierarchical consensus based on agent roles."""
        if not responses:
            return {"consensus": "No responses", "confidence": 0.0}

        # Role hierarchy (higher number = higher authority)
        role_hierarchy = {
            AgentRole.CHIEF_ANALYST: 9,
            AgentRole.RISK_MANAGER: 8,
            AgentRole.PORTFOLIO_MANAGER: 7,
            AgentRole.MARKET_ANALYST: 6,
            AgentRole.TECHNICAL_ANALYST: 5,
            AgentRole.STRATEGY_RESEARCHER: 4,
            AgentRole.EXECUTION_MANAGER: 3,
            AgentRole.NEWS_ANALYST: 2,
            AgentRole.COMPLIANCE_OFFICER: 1
        }

        # Weight responses by hierarchy
        weighted_values = []
        total_weight = 0

        for response in responses:
            agent = self.agents.get(response.agent_name)
            if agent:
                hierarchy_weight = role_hierarchy.get(agent.role, 1)
                decision_value = self._extract_numerical_sentiment(response.response)

                weighted_values.append(decision_value * hierarchy_weight)
                total_weight += hierarchy_weight

        if total_weight == 0:
            consensus_value = 0.5
            confidence = 0.5
        else:
            consensus_value = sum(weighted_values) / total_weight
            confidence = min(total_weight / (len(responses) * 5), 1.0)  # Normalize

        # Convert to decision
        if consensus_value > 0.6:
            consensus = "BUY"
        elif consensus_value < 0.4:
            consensus = "SELL"
        else:
            consensus = "HOLD"

        return {
            "consensus": consensus,
            "confidence": round(confidence, 4),
            "hierarchical_score": round(consensus_value, 4),
            "method": "hierarchical",
            "participating_agents": len(responses)
        }

    def _extract_numerical_sentiment(self, text: str) -> float:
        """Extract numerical sentiment from text (0=bearish, 1=bullish)."""
        text_lower = text.lower()

        # Positive indicators
        positive_words = ["buy", "bullish", "positive", "upward", "increase", "growth", "strong", "good"]
        negative_words = ["sell", "bearish", "negative", "downward", "decrease", "decline", "weak", "bad"]

        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)

        total_sentiment_words = positive_count + negative_count

        if total_sentiment_words == 0:
            return 0.5  # Neutral

        sentiment_score = positive_count / total_sentiment_words
        return sentiment_score

    def get_orchestration_metrics(self) -> Dict[str, Any]:
        """Get comprehensive orchestration metrics."""
        # Agent performance summary
        agent_metrics = {}
        for name, agent in self.agents.items():
            agent_metrics[name] = {
                "role": agent.role.value,
                "model": agent.model,
                "performance_score": round(agent.performance_score, 4),
                "total_calls": agent.total_calls,
                "success_rate": round(agent.successful_calls / agent.total_calls if agent.total_calls > 0 else 0, 4),
                "avg_response_time": round(agent.avg_response_time, 2),
                "active": agent.active
            }

        # System metrics
        total_calls = sum(agent.total_calls for agent in self.agents.values())
        total_successful = sum(agent.successful_calls for agent in self.agents.values())

        # Consensus metrics
        consensus_methods_used = {}
        if self.consensus_history:
            for record in self.consensus_history[-100:]:  # Last 100 consensus decisions
                method = record["method"]
                consensus_methods_used[method] = consensus_methods_used.get(method, 0) + 1

        return {
            "system_metrics": {
                "total_agents": len(self.agents),
                "active_agents": len([a for a in self.agents.values() if a.active]),
                "total_requests": total_calls,
                "successful_requests": total_successful,
                "system_success_rate": round(total_successful / total_calls if total_calls > 0 else 0, 4),
                "cache_size": len(self.response_cache),
                "consensus_decisions": len(self.consensus_history)
            },
            "agent_metrics": agent_metrics,
            "consensus_metrics": {
                "methods_used": consensus_methods_used,
                "total_consensus_decisions": len(self.consensus_history),
                "avg_participating_agents": round(np.mean([r["participating_agents"] for r in self.consensus_history[-50:]]) if self.consensus_history else 0, 1)
            },
            "performance_summary": {
                "top_performing_agent": max(self.agents.values(), key=lambda a: a.performance_score).name if self.agents else None,
                "fastest_agent": min(self.agents.values(), key=lambda a: a.avg_response_time if a.avg_response_time > 0 else float('inf')).name if self.agents else None,
                "most_active_agent": max(self.agents.values(), key=lambda a: a.total_calls).name if self.agents else None
            }
        }

    def optimize_agent_allocation(self) -> Dict[str, Any]:
        """Optimize agent allocation based on performance."""
        optimization_results = {}

        # Identify underperforming agents
        underperforming = [agent for agent in self.agents.values()
                          if agent.performance_score < 0.6 and agent.total_calls > 10]

        # Identify overloaded agents
        avg_calls = np.mean([agent.total_calls for agent in self.agents.values()])
        overloaded = [agent for agent in self.agents.values()
                     if agent.total_calls > avg_calls * 2]

        # Recommendations
        recommendations = []

        for agent in underperforming:
            recommendations.append(f"Consider retraining or replacing {agent.name} (performance: {agent.performance_score:.2f})")

        for agent in overloaded:
            recommendations.append(f"Consider load balancing for {agent.name} ({agent.total_calls} calls)")

        # Suggest optimal agent distribution
        role_distribution = {}
        for agent in self.agents.values():
            role = agent.role.value
            if role not in role_distribution:
                role_distribution[role] = {"count": 0, "avg_performance": 0}
            role_distribution[role]["count"] += 1
            role_distribution[role]["avg_performance"] += agent.performance_score

        for role_data in role_distribution.values():
            role_data["avg_performance"] /= role_data["count"]

        optimization_results = {
            "underperforming_agents": len(underperforming),
            "overloaded_agents": len(overloaded),
            "recommendations": recommendations,
            "role_distribution": role_distribution,
            "optimization_score": round(1 - (len(underperforming) + len(overloaded)) / len(self.agents), 4)
        }

        logger.info(f"🔧 Agent optimization completed: {len(recommendations)} recommendations")

        return optimization_results


# Test the Advanced AI Orchestration System
if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)

    async def test_orchestration():
        print("🤖 Testing Advanced AI Orchestration System")
        print("=" * 70)

        # Initialize orchestrator
        orchestrator = AdvancedAIOrchestrator()

        # Create test request
        test_request = AIRequest(
            request_id="test_001",
            agent_role=AgentRole.MARKET_ANALYST,
            prompt="Analyze current market conditions for Bitcoin",
            context={"symbol": "BTCUSDT", "price": 45000, "volume": 1000000},
            priority=1,
            timeout=15.0,
            timestamp=datetime.now(timezone.utc),
            metadata={"test": True}
        )

        print("📊 Testing single agent request...")
        response = await orchestrator.process_request(test_request)
        print(f"Response: {response.success}, Confidence: {response.confidence:.2f}")

        print("\n🤝 Testing multi-agent consensus...")
        consensus_roles = [AgentRole.MARKET_ANALYST, AgentRole.TECHNICAL_ANALYST, AgentRole.RISK_MANAGER]
        consensus = await orchestrator.multi_agent_consensus(
            test_request, consensus_roles, ConsensusMethod.CONFIDENCE_WEIGHTED
        )
        print(f"Consensus: {consensus.get('consensus', 'N/A')}, Confidence: {consensus.get('confidence', 0):.2f}")

        print("\n🎯 Testing intelligent routing...")
        routed_response = await orchestrator.intelligent_routing(test_request)
        print(f"Routed to: {routed_response.agent_name}, Success: {routed_response.success}")

        print("\n📈 Getting orchestration metrics...")
        metrics = orchestrator.get_orchestration_metrics()
        print(f"Active agents: {metrics['system_metrics']['active_agents']}")
        print(f"System success rate: {metrics['system_metrics']['system_success_rate']:.2%}")

        print("\n🔧 Running optimization...")
        optimization = orchestrator.optimize_agent_allocation()
        print(f"Optimization score: {optimization['optimization_score']:.2f}")

        print("\n✅ Advanced AI Orchestration test completed!")

    asyncio.run(test_orchestration())
